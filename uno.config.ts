import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetWind3,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

import { presetScrollbarHide } from 'unocss-preset-scrollbar-hide'

export default defineConfig({
  shortcuts: {
    // General
    'mobile-container': 'max-w-md mx-auto min-h-screen bg-background',
    'page-header': 'bg-white border-b border-border px-4 py-3 flex items-center justify-between sticky top-0 z-10',
    'page-content': 'p-4 pb-6',
    'exam-card': 'bg-card rounded-lg p-4 shadow-sm border border-border',

    // Buttons
    'btn': 'inline-flex items-center justify-center rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
    'btn-primary': 'btn bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3',
    'btn-outline': 'btn border border-primary text-primary hover:bg-primary hover:text-primary-foreground px-6 py-3',
    'btn-sm': 'px-4 py-2 text-sm',

    // Status
    'status-success': 'text-success bg-success-light border-success/20',
    'status-warning': 'text-warning bg-warning-light border-warning/20',
    'status-error': 'text-error bg-error-light border-error/20',

    // Timeline
    'timeline-container': 'relative',
    'timeline-line': 'absolute left-6 top-8 bottom-6 w-0.5 bg-gradient-to-b from-primary/30 via-primary/20 to-primary/10',
    'timeline-item': 'relative pl-16 pb-6',
    'timeline-dot': 'absolute left-4 top-6 w-5 h-5 rounded-full border-2 bg-background flex items-center justify-center z-10 shadow-sm',
    'timeline-dot-inner': 'w-2 h-2 rounded-full bg-white',
  },
  theme: {
    colors: {
      border: 'hsl(var(--border))',
      input: 'hsl(var(--input))',
      ring: 'hsl(var(--ring))',
      background: 'hsl(var(--background))',
      foreground: 'hsl(var(--foreground))',
      primary: {
        DEFAULT: 'hsl(var(--primary))',
        foreground: 'hsl(var(--primary-foreground))',
        light: 'hsl(var(--primary-light))',
        dark: 'hsl(var(--primary-dark))',
      },
      secondary: {
        DEFAULT: 'hsl(var(--secondary))',
        foreground: 'hsl(var(--secondary-foreground))',
      },
      destructive: {
        DEFAULT: 'hsl(var(--destructive))',
        foreground: 'hsl(var(--destructive-foreground))',
      },
      muted: {
        DEFAULT: 'hsl(var(--muted))',
        foreground: 'hsl(var(--muted-foreground))',
      },
      accent: {
        DEFAULT: 'hsl(var(--accent))',
        foreground: 'hsl(var(--accent-foreground))',
      },
      popover: {
        DEFAULT: 'hsl(var(--popover))',
        foreground: 'hsl(var(--popover-foreground))',
      },
      card: {
        DEFAULT: 'hsl(var(--card))',
        foreground: 'hsl(var(--card-foreground))',
      },
      success: {
        DEFAULT: 'hsl(var(--success))',
        foreground: 'hsl(var(--success-foreground))',
        light: 'hsl(var(--success-light))',
      },
      warning: {
        DEFAULT: 'hsl(var(--warning))',
        foreground: 'hsl(var(--warning-foreground))',
        light: 'hsl(var(--warning-light))',
      },
      error: {
        DEFAULT: 'hsl(var(--error))',
        foreground: 'hsl(var(--error-foreground))',
        light: 'hsl(var(--error-light))',
      },
    },
    borderRadius: {
      lg: `var(--radius-lg)`,
      md: `var(--radius)`,
      sm: 'var(--radius-sm)',
    },
  },
  content: {
    pipeline: {
      include: [
        // the default
        /\.(vue|svelte|[jt]sx|mdx?|astro|elm|php|phtml|html)($|\?)/,
        // include .ts .js files
        /\.(ts|js)($|\?)/,
      ],
    },
  },
  presets: [
    presetWind3(),
    presetScrollbarHide(),
    presetAttributify(),
    presetIcons({
      scale: 1.1,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    presetTypography(),
    // presetWebFonts({
    //   fonts: {
    //     sans: 'DM Sans',
    //     serif: 'DM Serif Display',
    //     mono: 'DM Mono',
    //   },
    // }),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  safelist: 'prose prose-sm m-auto text-left'.split(' '),
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
  ],
})
