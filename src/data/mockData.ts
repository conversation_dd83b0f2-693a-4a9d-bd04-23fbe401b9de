import studentAvatar from '@/assets/student-avatar-placeholder.jpg'

// 模拟学生数据
export const mockStudentInfo = {
  isBound: true,
  studentId: 'G37070220100101XXXX',
  idCard: '37070220100101XXXX',
  name: '张三',
  photoUrl: studentAvatar,
  gender: '男',
  middleSchool: '潍坊市实验中学',
  grade: '九年级',
  ethnicity: '汉族',
  contactPhone: '13812345678',
  postalCode: '261000',
  address: '山东省潍坊市奎文区幸福家园',
  guardian: {
    name: '张伟',
    relation: '父亲',
    phone: '13987654321',
  },
  examSubjects: ['数学', '英语', '物理', '化学', '道德与法治', '历史'],
}

// 系统配置
export const mockConfig = {
  scoreQueryStart: '2025-07-15 09:00:00',
  stages: {
    main: {
      title: '"3+4"高职、普高志愿',
      applicationStart: '2025-05-11 08:00:00',
      applicationEnd: '2025-05-15 20:00:00',
      resultStart: '2025-07-20 09:00:00',
      resultEnd: '2025-09-01 00:00:00',
    },
    vocational: {
      title: '职教高考班、五年制高职、中专技校志愿',
      applicationStart: '2025-06-26 08:00:00',
      applicationEnd: '2025-06-30 20:00:00',
      resultStart: '2025-07-27 09:00:00',
      resultEnd: '2025-09-01 00:00:00',
    },
    private_supplementary: {
      title: '民办普高二次补录',
      applicationStart: '2025-07-17 08:00:00',
      applicationEnd: '2025-07-18 20:00:00',
      resultStart: '2025-07-20 09:00:00',
      resultEnd: '2025-09-01 00:00:00',
    },
    vocational_supplementary: {
      title: '职教高考班和五年制高职二次补录',
      applicationStart: '2025-07-31 08:00:00',
      applicationEnd: '2025-09-01 00:00:00',
      resultStart: '2025-08-03 09:00:00',
      resultEnd: '2025-09-01 00:00:00',
    },
  },
}
