/* 潍坊高中段综合服务平台设计系统 - 移动端优化 */

:root {
  /* 主色调 - 教育蓝 */
  --primary: 214 100% 45%;
  --primary-foreground: 0 0% 100%;
  --primary-light: 214 100% 55%;
  --primary-dark: 214 100% 35%;

  /* 背景色系 */
  --background: 210 20% 98%;
  --foreground: 222 47% 11%;

  /* 卡片系统 */
  --card: 0 0% 100%;
  --card-foreground: 222 47% 11%;
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /* 辅助色彩 */
  --secondary: 210 17% 95%;
  --secondary-foreground: 222 47% 11%;

  --success: 142 76% 36%;
  --success-foreground: 0 0% 100%;
  --success-light: 142 76% 96%;

  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 100%;
  --warning-light: 38 92% 96%;

  --error: 0 84% 60%;
  --error-foreground: 0 0% 100%;
  --error-light: 0 84% 96%;

  --muted: 210 17% 95%;
  --muted-foreground: 215 16% 47%;

  /* 表单元素 */
  --border: 220 13% 91%;
  --input: 0 0% 100%;
  --input-border: 220 13% 91%;
  --ring: 214 100% 45%;

  /* 圆角 */
  --radius: 0.75rem;
  --radius-sm: 0.5rem;
  --radius-lg: 1rem;

  /* 移动端专用间距 */
  --mobile-padding: 1rem;
  --mobile-gap: 0.75rem;

  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
  --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--secondary)));
}

.dark {
  /* 暗色模式适配 */
  --background: 222 47% 7%;
  --foreground: 210 40% 98%;
  --card: 222 47% 9%;
  --card-foreground: 210 40% 98%;
  --primary: 214 100% 55%;
  --secondary: 217 33% 17%;
  --muted: 217 33% 17%;
  --muted-foreground: 215 20% 65%;
  --border: 217 33% 17%;
  --input: 217 33% 17%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
