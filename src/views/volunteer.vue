<script setup lang="ts">
import PageHeader from '@/components/PageHeader.vue'
import { mockConfig } from '@/data/mockData'

const route = useRoute()
const type = route.params.type as string
const stageInfo = type ? mockConfig.stages[type as keyof typeof mockConfig.stages] : null
</script>

<template>
  <div class="min-h-screen">
    <PageHeader :title="stageInfo?.title || '志愿填报'" />

    <div class="page-content">
      <p class="text-muted-foreground">
        当前阶段：{{ type }}
      </p>
      <p class="text-muted-foreground">
        通过 type 参数区分不同阶段的志愿填报
      </p>
    </div>
  </div>
</template>
