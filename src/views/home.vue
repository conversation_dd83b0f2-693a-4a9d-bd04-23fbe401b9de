<script setup lang="ts">
import type { StageInfo } from '@/types/volunteer.type'
import PageHeader from '@/components/PageHeader.vue'
import StudentInfoCard from '@/components/StudentInfoCard.vue'
import TimelineVolunteerStageCard from '@/components/TimelineVolunteerStageCard.vue'
import { mockConfig, mockStudentInfo } from '@/data/mockData'

const router = useRouter()

function handleProfileClick() {
  router.push('/profile')
}

function handleScoreClick() {
  router.push('/score')
}
</script>

<template>
  <div class="min-h-screen">
    <!-- 页面标题栏 -->
    <PageHeader title="潍坊高中段综合服务平台" :show-back="false" />

    <div class="page-content space-y-6">
      <!-- 副标题 -->
      <div class="text-center">
        <p class="text-muted-foreground">
          2025 年高中阶段学校招生考试
        </p>
      </div>

      <!-- 学生信息卡片 -->
      <StudentInfoCard :student-info="mockStudentInfo">
        <template #actions>
          <div class="flex gap-3">
            <button
              class="btn-outline flex-1 btn-sm"
              @click="handleProfileClick"
            >
              <div class="i-lucide-user mr-2 h-4 w-4" />
              <span>完善信息</span>
            </button>
            <button
              class="btn-outline flex-1 btn-sm"
              @click="handleScoreClick"
            >
              <div class="i-lucide-file-text mr-2 h-4 w-4" />
              <span>分数查询</span>
            </button>
          </div>
        </template>
      </StudentInfoCard>

      <!-- 志愿填报阶段卡片区域 - 时间轴布局 -->
      <div class="space-y-4">
        <h2 class="text-lg font-semibold">
          志愿填报
        </h2>

        <div class="timeline-container">
          <!-- 时间轴垂直线 -->
          <div class="timeline-line" />

          <!-- 时间轴内容 -->
          <div>
            <TimelineVolunteerStageCard
              v-for="([stageKey, stageInfo]) in Object.entries(mockConfig.stages)"
              :key="stageKey"
              :stage-key="stageKey"
              :stage-info="stageInfo as StageInfo"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
