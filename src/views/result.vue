<script setup lang="ts">
import PageHeader from '@/components/PageHeader.vue'
import { mockConfig } from '@/data/mockData'

const route = useRoute()
const type = route.params.type as string
const stageInfo = type ? mockConfig.stages[type as keyof typeof mockConfig.stages] : null
</script>

<template>
  <div class="min-h-screen">
    <PageHeader :title="`${stageInfo?.title || '录取结果'} - 录取结果`" />

    <div class="page-content">
      <p class="text-muted-foreground">
        当前阶段：{{ type }}
      </p>
      <p class="text-muted-foreground">
        通过 type 参数查询不同阶段的录取结果
      </p>
    </div>
  </div>
</template>
