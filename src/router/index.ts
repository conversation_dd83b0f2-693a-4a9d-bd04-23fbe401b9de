import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Index',
    component: () => import('~/views/home.vue'),
    meta: { title: import.meta.env.VITE_TITLE, layout: 'default' },
  },
  {
    path: '/result',
    name: 'Result',
    component: () => import('~/views/result.vue'),
    meta: { title: `查询结果 - ${import.meta.env.VITE_TITLE}`, layout: 'default' },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('~/views/not-found.vue'),
    meta: { title: `页面未找到 - ${import.meta.env.VITE_TITLE}`, layout: 'empty' },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes,
})

router.beforeEach((to, from) => {

})

export default router
