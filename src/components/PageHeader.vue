<script setup lang="ts">
interface PageHeaderProps {
  title: string
  showBack?: boolean
  onBack?: () => void
}

const props = withDefaults(defineProps<PageHeaderProps>(), {
  showBack: true,
})

const router = useRouter()

function handleBack() {
  if (props.onBack) {
    props.onBack()
  }
  else {
    router.back()
  }
}
</script>

<template>
  <header class="page-header">
    <div class="flex items-center gap-3">
      <button
        v-if="props.showBack"
        class="h-8 w-8 flex items-center justify-center rounded-md p-2 active:bg-gray-200 hover:bg-gray-100"
        @click="handleBack"
      >
        <div class="i-mingcute:arrow-left-line h-4 w-4" />
      </button>
      <h1 class="truncate text-lg text-foreground font-semibold">
        {{ props.title }}
      </h1>
    </div>
  </header>
</template>
