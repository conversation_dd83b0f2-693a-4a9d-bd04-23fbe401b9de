<script setup lang="ts">
// 定义组件的 props
const props = defineProps<{
  startTime: string
  endTime: string
}>()

// 定义组件发出的事件
const emit = defineEmits<{
  (e: 'statusChange', status: 'pending' | 'active' | 'ended'): void
}>()

// 倒计时剩余时间
const timeLeft = ref('')
// 当前状态
const status = ref<'pending' | 'active' | 'ended'>('pending')
// 定时器引用
let interval: number | undefined

// 根据状态返回不同的样式类
const statusStyle = computed(() => {
  switch (status.value) {
    case 'pending':
      return 'text-warning bg-warning-light border-warning/20'
    case 'active':
      return 'text-success bg-success-light border-success/20'
    case 'ended':
      return 'text-muted-foreground bg-muted border-border'
    default:
      return 'text-muted-foreground bg-muted border-border'
  }
})

// 更新倒计时的函数
function updateCountdown() {
  const now = new Date().getTime()
  const start = new Date(props.startTime).getTime()
  const end = new Date(props.endTime).getTime()

  let newStatus: 'pending' | 'active' | 'ended'
  let timeText = ''

  if (now < start) {
    // 未开始
    newStatus = 'pending'
    const distance = start - now
    const days = Math.floor(distance / (1000 * 60 * 60 * 24))
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0)
      timeText = `距开始 ${days}天${hours}小时`
    else if (hours > 0)
      timeText = `距开始 ${hours}小时${minutes}分钟`
    else
      timeText = `距开始 ${minutes}分钟`
  }
  else if (now < end) {
    // 进行中
    newStatus = 'active'
    const distance = end - now
    const days = Math.floor(distance / (1000 * 60 * 60 * 24))
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0)
      timeText = `剩余 ${days}天${hours}小时`
    else if (hours > 0)
      timeText = `剩余 ${hours}小时${minutes}分钟`
    else
      timeText = `剩余 ${minutes}分钟`
  }
  else {
    // 已结束
    newStatus = 'ended'
    timeText = '已结束'
    if (interval)
      clearInterval(interval)
  }

  status.value = newStatus
  timeLeft.value = timeText
  emit('statusChange', newStatus)
}

// 监听 props 的变化，如果时间变化则重新开始倒计时
watch(() => [props.startTime, props.endTime], () => {
  if (interval)
    clearInterval(interval)
  updateCountdown()
  interval = setInterval(updateCountdown, 60000)
})

onMounted(() => {
  updateCountdown()
  interval = setInterval(updateCountdown, 60000) // 每分钟更新一次
})

onUnmounted(() => {
  if (interval)
    clearInterval(interval) // 组件卸载时清除定时器
})
</script>

<template>
  <span class="border rounded px-2 py-1 text-xs" :class="[statusStyle]">
    {{ timeLeft }}
  </span>
</template>
