<script setup lang="ts">
// 定义组件的 props
interface StudentInfo {
  studentId: string
  idCard: string
  name: string
  photoUrl?: string
  gender: string
  middleSchool: string
  grade: string
  ethnicity: string
}

interface Props {
  studentInfo: StudentInfo
  showDetails?: boolean
}

withDefaults(defineProps<Props>(), {
  showDetails: true,
})
</script>

<template>
  <div class="exam-card border-primary/20 from-primary/5 to-primary-light/5 bg-gradient-to-r">
    <div class="flex items-start gap-4">
      <!-- 头像区域 -->
      <div class="flex-shrink-0">
        <img
          v-if="studentInfo.photoUrl"
          :src="studentInfo.photoUrl"
          :alt="`${studentInfo.name}的照片`"
          class="h-24 w-20 border-2 border-primary/20 rounded-lg bg-muted object-cover"
        >
        <div v-else class="h-24 w-20 flex items-center justify-center border-2 border-primary/20 rounded-lg bg-muted">
          <div class="i-lucide-user h-8 w-8 text-muted-foreground" />
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="min-w-0 flex-1">
        <div class="mb-2 flex items-center gap-2">
          <h3 class="truncate text-lg text-foreground font-semibold">
            {{ studentInfo.name }}
          </h3>
          <span class="rounded bg-muted px-2 py-1 text-sm text-muted-foreground">
            {{ studentInfo.gender }}
          </span>
        </div>

        <div class="text-sm space-y-1.5">
          <div class="flex items-center gap-2 text-muted-foreground">
            <div class="i-lucide-id-card h-4 w-4 flex-shrink-0" />
            <span class="truncate">学籍号：{{ studentInfo.studentId }}</span>
          </div>

          <div class="flex items-center gap-2 text-muted-foreground">
            <div class="i-lucide-id-card h-4 w-4 flex-shrink-0" />
            <span class="truncate">身份证：{{ studentInfo.idCard }}</span>
          </div>

          <template v-if="showDetails">
            <div class="flex items-center gap-2 text-muted-foreground">
              <div class="i-lucide-school h-4 w-4 flex-shrink-0" />
              <span class="truncate">{{ studentInfo.middleSchool }}</span>
            </div>

            <div class="flex gap-4 text-xs text-muted-foreground">
              <span>{{ studentInfo.grade }}</span>
              <span>{{ studentInfo.ethnicity }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div v-if="$slots.actions" class="mt-4 border-t pt-4">
      <slot name="actions" />
    </div>
  </div>
</template>
