<script setup lang="ts">
import type { StageInfo } from '@/types/volunteer.type'

type Status = 'pending' | 'active' | 'ended'

const props = defineProps<{
  stageKey: string
  stageInfo: StageInfo
}>()

const router = useRouter()

const applicationStatus = ref<Status>('pending')
const resultStatus = ref<Status>('pending')

// 格式化日期时间显示
function formatDateTime(dateTimeStr: string) {
  const date = new Date(dateTimeStr)
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes().toString().padStart(2, '0')
  return `${month}月${day}日 ${hour}:${minute}`
}

function handleVolunteerClick() {
  router.push(`/volunteer/${props.stageKey}`)
}

function handleResultClick() {
  router.push(`/result/${props.stageKey}`)
}

const applicationButtonProps = computed(() => {
  switch (applicationStatus.value) {
    case 'pending':
      return { disabled: true, class: 'btn-outline', text: '未开始' }
    case 'active':
      return { disabled: false, class: 'btn-default', text: '填报志愿' }
    case 'ended':
      return { disabled: true, class: 'btn-outline', text: '已结束' }
    default:
      // exhaustive check
      return { disabled: true, class: 'btn-outline', text: '未知状态' }
  }
})

const resultButtonProps = computed(() => {
  switch (resultStatus.value) {
    case 'pending':
      return { disabled: true, class: 'btn-outline', text: '未开始' }
    case 'active':
      return { disabled: false, class: 'btn-default', text: '查询结果' }
    case 'ended':
      return { disabled: true, class: 'btn-outline', text: '已结束' }
    default:
      // exhaustive check
      return { disabled: true, class: 'btn-outline', text: '未知状态' }
  }
})
</script>

<template>
  <div class="exam-card">
    <div class="flex flex-col gap-4">
      <!-- 标题和状态 -->
      <div class="flex items-start justify-between gap-3">
        <h3 class="flex-1 text-base text-foreground font-medium leading-tight">
          {{ stageInfo.title }}
        </h3>
      </div>

      <!-- 填报阶段 -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <div class="i-mingcute:time-line h-4 w-4 text-muted-foreground" />
          <span class="text-sm text-muted-foreground">志愿填报</span>
          <CountdownTimer
            :start-time="stageInfo.applicationStart"
            :end-time="stageInfo.applicationEnd"
            @status-change="applicationStatus = $event"
          />
        </div>
        <div class="text-xs text-muted-foreground">
          {{ formatDateTime(stageInfo.applicationStart) }} - {{ formatDateTime(stageInfo.applicationEnd) }}
        </div>
      </div>

      <!-- 结果查询阶段 -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <div class="i-mingcute:time-line h-4 w-4 text-muted-foreground" />
          <span class="text-sm text-muted-foreground">结果查询</span>
          <CountdownTimer
            :start-time="stageInfo.resultStart"
            :end-time="stageInfo.resultEnd"
            @status-change="resultStatus = $event"
          />
        </div>
        <div class="text-xs text-muted-foreground">
          {{ formatDateTime(stageInfo.resultStart) }} - {{ formatDateTime(stageInfo.resultEnd) }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-3 pt-2">
        <button
          class="btn flex-1 btn-sm" :class="[applicationButtonProps.class]"
          :disabled="applicationButtonProps.disabled"
          @click="handleVolunteerClick"
        >
          <div class="i-mingcute:file-edit-line mr-2 h-4 w-4" />
          {{ applicationButtonProps.text }}
        </button>
        <button
          class="btn flex-1 btn-sm" :class="[resultButtonProps.class]"
          :disabled="resultButtonProps.disabled"
          @click="handleResultClick"
        >
          <div class="i-mingcute:search-line mr-2 h-4 w-4" />
          {{ resultButtonProps.text }}
        </button>
      </div>
    </div>
  </div>
</template>
