<script setup lang="ts">
import type { StageInfo } from '@/types/volunteer.type'
import VolunteerStageCard from './VolunteerStageCard.vue'

// 定义组件的 props
const props = defineProps<{
  stageKey: string
  stageInfo: StageInfo
}>()

// 计算整体阶段状态，用于时间轴圆点显示
const overallStatus = computed(() => {
  const now = Date.now()
  const appStart = new Date(props.stageInfo.applicationStart).getTime()
  const resultEnd = new Date(props.stageInfo.resultEnd).getTime()

  if (now < appStart)
    return 'pending'
  if (now > resultEnd)
    return 'ended'
  return 'active'
})
</script>

<template>
  <div class="relative pl-8">
    <!-- 时间轴线 -->
    <div class="absolute bottom-0 left-[7px] top-0 w-0.5 bg-border" />

    <!-- 时间轴圆点 -->
    <div
      class="absolute left-0 top-5 h-4 w-4 flex items-center justify-center rounded-full"
      :class="{
        'bg-primary ring-4 ring-primary/30': overallStatus === 'active',
        'bg-success ring-4 ring-success/30': overallStatus === 'ended',
        'bg-muted-foreground/50': overallStatus === 'pending',
      }"
    >
      <div
        v-if="overallStatus === 'active'"
        class="h-2 w-2 animate-pulse rounded-full bg-white"
      />
      <div
        v-if="overallStatus === 'ended'"
        class="i-mingcute:check-fill h-2.5 w-2.5 text-white"
      />
    </div>

    <!-- 卡片内容 -->
    <div class="relative">
      <VolunteerStageCard
        :stage-key="stageKey"
        :stage-info="stageInfo"
        class="mb-6"
      />
    </div>
  </div>
</template>
